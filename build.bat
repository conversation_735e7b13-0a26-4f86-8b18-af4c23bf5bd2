@echo off
REM 调度模块编译脚本

echo ===== 调度模块编译脚本 =====

REM 设置环境变量
set LIB_DIR=lib
set SRC_DIR=src\main\java
set RESOURCES_DIR=src\main\resources
set BUILD_DIR=build
set CLASSES_DIR=%BUILD_DIR%\classes
set DIST_DIR=%BUILD_DIR%\dist

REM 创建构建目录
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %CLASSES_DIR% mkdir %CLASSES_DIR%
if not exist %DIST_DIR% mkdir %DIST_DIR%

echo 1. 准备类路径...
set CLASSPATH=
for %%f in (%LIB_DIR%\*.jar) do set CLASSPATH=!CLASSPATH!;%%f
set CLASSPATH=%CLASSPATH%;%CLASSES_DIR%

echo 2. 编译Java源文件（使用GBK编码）...
javac -encoding GBK -cp "%CLASSPATH%" -d %CLASSES_DIR% -sourcepath %SRC_DIR% %SRC_DIR%\com\scheduler\*.java %SRC_DIR%\com\scheduler\model\*.java %SRC_DIR%\com\scheduler\config\*.java %SRC_DIR%\com\scheduler\service\*.java %SRC_DIR%\com\scheduler\core\*.java

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 3. 复制资源文件...
xcopy /Y /S %RESOURCES_DIR%\* %CLASSES_DIR%\

echo 4. 创建JAR文件...
cd %CLASSES_DIR%
jar cfm ..\dist\scheduler.jar ..\..\..\manifest.txt com\

cd ..\..
echo 5. 复制依赖库...
xcopy /Y %LIB_DIR%\*.jar %DIST_DIR%\

echo ===== 编译完成 =====
echo JAR文件位置: %DIST_DIR%\scheduler.jar
echo 依赖库位置: %DIST_DIR%\
echo.
echo 运行命令示例:
echo java -cp "%DIST_DIR%\*" com.scheduler.SchedulerApplication
echo.
echo 测试命令示例:
echo java -cp "%DIST_DIR%\*" com.scheduler.SchedulerApplication test
echo.
pause