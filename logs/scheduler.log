2025-09-12 14:53:46 INFO  [main] ConfigXmlUtilTest - === 开始测试ConfigXmlUtil ===
2025-09-12 14:53:46 INFO  [main] ConfigXmlUtil - ConfigXmlUtil初始化完成，配置文件路径: config.xml
2025-09-12 14:53:46 INFO  [main] ConfigXmlUtilTest - --- 测试1：获取platform为'test1'的所有服务 ---
2025-09-12 14:53:46 INFO  [main] ConfigXmlUtil - 获取platform为 'test1' 的所有服务
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 成功解析配置文件: config.xml
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='24', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='40', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='33', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='37', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='filesync', scheduleid='27', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='18', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='20', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='mq', scheduleid='23', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='25', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='39', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='21', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='mq', scheduleid='46', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtil - 找到 12 个platform为 'test1' 的服务
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest - 找到 12 个platform为'test1'的服务:
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='24', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='40', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='33', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='37', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='filesync', scheduleid='27', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='18', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='20', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='mq', scheduleid='23', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='25', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='39', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='21', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='mq', scheduleid='46', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest - --- 测试2：获取所有有效服务 ---
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtil - 获取所有有效服务信息
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 成功解析配置文件: config.xml
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='24', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='40', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='33', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='37', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='filesync', scheduleid='27', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='18', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='20', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='mq', scheduleid='23', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='25', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='39', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='udp', scheduleid='21', platform='test1'}
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 找到有效服务: ConfigServiceInfo{servicetype='mq', scheduleid='46', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtil - 找到 12 个有效服务
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest - 找到 12 个有效服务:
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='24', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='40', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='33', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='37', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='filesync', scheduleid='27', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='18', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='20', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='mq', scheduleid='23', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='25', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='39', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='udp', scheduleid='21', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest -   ConfigServiceInfo{servicetype='mq', scheduleid='46', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest - --- 测试3：根据scheduleid获取服务信息 ---
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtil - 获取scheduleid为 '24' 的服务信息
2025-09-12 14:53:47 DEBUG [main] ConfigXmlUtil - 成功解析配置文件: config.xml
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='24', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtilTest - scheduleid为'24'的服务: ConfigServiceInfo{servicetype='udp', scheduleid='24', platform='test1'}
2025-09-12 14:53:47 INFO  [main] ConfigXmlUtil - 获取scheduleid为 '18' 的服务信息
2025-09-12 14:53:48 DEBUG [main] ConfigXmlUtil - 成功解析配置文件: config.xml
2025-09-12 14:53:48 INFO  [main] ConfigXmlUtil - 找到匹配服务: ConfigServiceInfo{servicetype='udp', scheduleid='18', platform='test1'}
2025-09-12 14:53:48 INFO  [main] ConfigXmlUtilTest - scheduleid为'18'的服务: ConfigServiceInfo{servicetype='udp', scheduleid='18', platform='test1'}
2025-09-12 14:53:48 INFO  [main] ConfigXmlUtil - 获取scheduleid为 '999' 的服务信息
2025-09-12 14:53:48 DEBUG [main] ConfigXmlUtil - 成功解析配置文件: config.xml
2025-09-12 14:53:48 WARN  [main] ConfigXmlUtil - 未找到scheduleid为 '999' 的服务
2025-09-12 14:53:48 INFO  [main] ConfigXmlUtilTest - 未找到scheduleid为'999'的有效服务
2025-09-12 14:53:48 INFO  [main] ConfigXmlUtilTest - === ConfigXmlUtil测试完成 ===
