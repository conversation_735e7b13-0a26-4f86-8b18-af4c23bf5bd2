<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE preferences SYSTEM 'http://java.sun.com/dtd/preferences.dtd'>
<preferences EXTERNAL_XML_VERSION="1.0">
<root type="system">
<map/>
<node name="apps">
<map>
<entry key="servicenum" value="80"/>
<entry key="servicecount" value="47"/>
<entry key="isSrc" value="true"/>
<entry key="appcount" value="BTM0_app5"/>
<entry key="channelcount" value="0"/>
</map>
<node name="BTM0_app1">
<map>
<entry key="departmentName" value="11"/>
<entry key="competentPhone" value="11"/>
<entry key="isImport" value="false"/>
<entry key="competentName" value="11"/>
<entry key="displayname" value="11"/>
</map>
<node name="24">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="11"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="fspfsp"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="24"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
<node name="40">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="13"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="tcptest"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="40"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
</node>
<node name="BTM0_app2">
<map>
<entry key="departmentName" value="1"/>
<entry key="competentPhone" value="1"/>
<entry key="isImport" value="false"/>
<entry key="competentName" value="1"/>
<entry key="displayname" value="cyc"/>
</map>
<node name="33">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="11"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="cc11e"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="33"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
<node name="37">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value=""/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="0"/>
<entry key="displayname" value="cctcp"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="37"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
<node name="27">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="2"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="filesync"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="ftp"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="27"/>
<entry key="flowlevel" value="10240"/>
</map>
</node>
<node name="38">
<map>
<entry key="servicetype" value="dbsync-local"/>
<entry key="creator" value="unimas"/>
<entry key="configedtime" value="0"/>
<entry key="displayname" value="aac"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="templateid" value=""/>
<entry key="seclevel" value="4"/>
<entry key="importServiceId" value=""/>
<entry key="status" value="0"/>
<entry key="isoverall" value="false"/>
</map>
</node>
<node name="18">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="11"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="cc_fsp"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="true"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="18"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
<node name="20">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="11"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="udp"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="20"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
</node>
<node name="BTM0_app3">
<map>
<entry key="departmentName" value="1"/>
<entry key="competentPhone" value="1"/>
<entry key="isImport" value="false"/>
<entry key="competentName" value="1"/>
<entry key="displayname" value="vg418419"/>
</map>
<node name="23">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value=""/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="mq"/>
<entry key="configedtime" value="0"/>
<entry key="displayname" value="kafka"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="23"/>
<entry key="flowlevel" value="10240"/>
</map>
</node>
<node name="25">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="11"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="1"/>
<entry key="displayname" value="418"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="true"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="25"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
<node name="39">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value=""/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="0"/>
<entry key="displayname" value="FSP_send"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="39"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
<node name="21">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value="12"/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="udp"/>
<entry key="configedtime" value="3"/>
<entry key="displayname" value="udd"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="21"/>
<entry key="flowlevel" value="10"/>
</map>
</node>
</node>
<node name="BTM0_app5">
<map>
<entry key="departmentName" value="test"/>
<entry key="competentPhone" value="13"/>
<entry key="isImport" value="false"/>
<entry key="competentName" value="test"/>
<entry key="displayname" value="test"/>
</map>
<node name="46">
<map>
<entry key="creator" value="unimas"/>
<entry key="isAudit" value="true"/>
<entry key="templateid" value=""/>
<entry key="platform" value="test1"/>
<entry key="type" value=""/>
<entry key="importServiceId" value=""/>
<entry key="servicetype" value="mq"/>
<entry key="configedtime" value="0"/>
<entry key="displayname" value="mq"/>
<entry key="secstate" value="1"/>
<entry key="isRun" value="false"/>
<entry key="istemplate" value="false"/>
<entry key="seclevel" value="4"/>
<entry key="scheduleid" value="46"/>
<entry key="flowlevel" value="10240"/>
</map>
</node>
</node>
</node>
</root>
</preferences>
