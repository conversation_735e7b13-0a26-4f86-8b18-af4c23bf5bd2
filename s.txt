调度模块设计说明：
功能概要：
	该模块负责节点状态监控，节点异常时，负责服务的切换，切换的逻辑是，先判断是否有正常节点运行，找到正常节点后，停止异常节点所有正在运行的服务，然后
	启动正常节点上所有对应服务。
类和接口说明：
1：配置信息获取类，该类主要负责读取配置和系统信息，有如下两个接口
	1：节点获取接口
		去读本地配置文件：/etc/unimas/tomcat/conf/platforms.xml获得信息，示例格式如下：
		<?xml version="1.0" encoding="GBK"?>
		<platforms>
			<platform>
				<name>平台A</name>
				<devid>设备号</devid>
				<address>************</address> 
				<port>8080</port>
				<mode>nor</mode>
				<id>201</id>
				<nicid>eth3</nicid>
				<virtualip>*************</virtualip>
				<netmask>*************</netmask>
			</platform>
			<platform> 
				<name>平台B</name>
				<devid>设备号</devid>
				<address>************</address> 
				<port>8081</port> 
				<mode>nor</mode>
				<id>201</id>
				<nicid>eth3</nicid>
				<virtualip>*************</virtualip>
				<netmask>*************</netmask>
			</platform>
		</platforms>


	2：节点状态接口
		该接口从状态机获取所有节点信息,发送一个http得get请求,地址端口固定是127.0.0.1和9001
		body内容中的各个字段使用&号分割；key和value用=号连接，字段名如下：
		bussinessM：主机业务口状态，1表示正常，0表示异常
		isolateM：主机隔离口状态，1表示正常，0表示异常
		authoriteM：主机授权状态，1表示正常，0表示异常
		recvstatusM：主机接收端状态，1表示正常，0表示异常

		bussinessB：备机业务口状态，1表示正常，0表示异常
		isolateB：备机隔离口状态，1表示正常，0表示异常
		authoriteB：备机授权状态，1表示正常，0表示异常
		recvstatusB：备机接收端状态，1表示正常，0表示异常
2：服务操作类：该类主要服务对服务启停等操作，有如下接口
	1：启动服务接口
		该接口暂时预留
	2：启动所有服务接口
		该接口暂时预留
	3：停止服务接口
		该接口暂时预留
	4：停止所有服务接口
		该接口暂时预留
	5：服务数控制接口
		一个节点可以启动的服务是有限的，该接口主要判断是否可以继续启动服务，目前暂时可以不实现，返回true
3：服务调度类：该类主要负责服务的调度
	此处需要单独设计一个线程，线程逻辑如下：
	1：调用节点状态接口获取状态
	2：判断本机状态是否可用，判断是否有其他可用节点可用，根据情况开启切换服务逻辑
	3：停止所有原先启动的服务
	4：启动所有服务
	5：结束一次循环
	
	

	