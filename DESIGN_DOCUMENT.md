# 调度模块设计文档

## 1. 概述

### 1.1 目的
调度模块负责节点状态监控，节点异常时负责服务的切换。切换逻辑按以下顺序执行：
1. 判断是否有正常节点可用
2. 停止异常节点所有正在运行的服务
3. 启动正常节点上所有对应服务

### 1.2 技术要求
- 基于JDK 8
- 只使用`lib`目录下可用的第三方库
- 遵循面向对象设计原则
- 项目编码：GBK

## 2. 系统架构

### 2.1 核心组件
1. **配置信息管理类** - 处理配置和系统信息获取
2. **服务操作类** - 管理服务启停操作
3. **服务调度类** - 协调服务调度逻辑

### 2.2 使用的关键库
- `commons-configuration-1.6.jar` - 配置文件解析
- `commons-logging-1.1.1.jar` - 日志框架
- `dom4j-1.4.jar` - XML解析
- `json-lib-2.2.2-jdk15.jar` - JSON处理
- `log4j-1.2.16.jar` - 日志实现

## 3. 详细设计

### 3.1 配置信息管理类

#### 3.1.1 类：`ConfigurationManager`

**目的**：管理配置文件读取和系统信息获取。

**依赖项**：
- `dom4j` 用于XML解析
- `commons-logging` 用于日志记录

**接口**：

##### ******* 节点获取接口
```java
public List<PlatformNode> getNodes()
```

**功能**：
- 读取本地配置文件：`/etc/unimas/tomcat/conf/platforms.xml`
- 解析包含平台信息的XML结构
- 返回PlatformNode对象列表

**XML格式**：
```xml
<?xml version="1.0" encoding="GBK"?>
<platforms>
    <platform>
        <name>平台A</name>
        <devid>设备号</devid>
        <address>************</address>
        <port>8080</port>
        <mode>nor</mode>
        <id>201</id>
        <nicid>eth3</nicid>
        <virtualip>*************</virtualip>
        <netmask>*************</netmask>
    </platform>
</platforms>
```

##### ******* 节点状态接口
```java
public NodeStatus getNodeStatus()
```

**功能**：
- 向固定地址发送HTTP GET请求：`127.0.0.1:9001`
- 解析响应体，字段用`&`分隔，键值用`=`连接
- 返回包含所有状态信息的NodeStatus对象

**状态字段**：
- `bussinessM`：主机业务口状态（1=正常，0=异常）
- `isolateM`：主机隔离口状态（1=正常，0=异常）
- `authoriteM`：主机授权状态（1=正常，0=异常）
- `recvstatusM`：主机接收端状态（1=正常，0=异常）
- `bussinessB`：备机业务口状态（1=正常，0=异常）
- `isolateB`：备机隔离口状态（1=正常，0=异常）
- `authoriteB`：备机授权状态（1=正常，0=异常）
- `recvstatusB`：备机接收端状态（1=正常，0=异常）

### 3.2 服务操作类

#### 3.2.1 类：`ServiceOperationManager`

**目的**：管理节点上的服务启停操作。

**接口**：

##### 3.2.1.1 启动服务接口
```java
public boolean startService(String serviceName, PlatformNode node)
```
**状态**：预留待实现

##### 3.2.1.2 启动所有服务接口
```java
public boolean startAllServices(PlatformNode node)
```
**状态**：预留待实现

##### 3.2.1.3 停止服务接口
```java
public boolean stopService(String serviceName, PlatformNode node)
```
**状态**：预留待实现

##### 3.2.1.4 停止所有服务接口
```java
public boolean stopAllServices(PlatformNode node)
```
**状态**：预留待实现

##### 3.2.1.5 服务数控制接口
```java
public boolean canStartMoreServices(PlatformNode node)
```
**功能**：
- 判断节点是否可以启动更多服务
- 目前返回`true`（未实现限制检查）
- 未来实现将检查节点容量限制

### 3.3 服务调度类

#### 3.3.1 类：`ServiceScheduler`

**目的**：在专用线程中运行的主要调度逻辑协调器。

**依赖项**：
- `ConfigurationManager` 用于节点和状态信息
- `ServiceOperationManager` 用于服务操作

**线程逻辑**：

1. **状态获取**：调用节点状态接口获取当前状态
2. **可用性评估**：
   - 检查本机状态可用性
   - 识别可用的备选节点
   - 确定是否需要服务切换
3. **服务迁移**：
   - 停止当前节点上所有先前启动的服务
   - 在可用目标节点上启动所有服务
4. **循环完成**：结束当前调度循环

#### 3.3.2 调度算法

```java
public void run() {
    while (!Thread.currentThread().isInterrupted()) {
        try {
            // 步骤1：获取当前节点状态
            NodeStatus status = configManager.getNodeStatus();
            
            // 步骤2：评估当前节点并查找备选方案
            boolean currentNodeHealthy = evaluateNodeHealth(status);
            List<PlatformNode> availableNodes = findHealthyNodes();
            
            // 步骤3：如需要则执行服务切换
            if (!currentNodeHealthy && !availableNodes.isEmpty()) {
                performServiceSwitching(availableNodes.get(0));
            }
            
            // 步骤4：等待下一个循环
            Thread.sleep(schedulingInterval);
            
        } catch (Exception e) {
            logger.error("调度循环失败", e);
        }
    }
}
```

## 4. 数据模型

### 4.1 PlatformNode
```java
public class PlatformNode {
    private String name;        // 平台名称
    private String deviceId;    // 设备号
    private String address;     // 地址
    private int port;           // 端口
    private String mode;        // 模式
    private int id;             // ID
    private String nicId;       // 网卡ID
    private String virtualIp;   // 虚拟IP
    private String netmask;     // 网络掩码
    // getters and setters
}
```

### 4.2 NodeStatus
```java
public class NodeStatus {
    private int businessM;      // 主机业务口状态
    private int isolateM;       // 主机隔离口状态
    private int authorityM;     // 主机授权状态
    private int recvStatusM;    // 主机接收端状态
    private int businessB;      // 备机业务口状态
    private int isolateB;       // 备机隔离口状态
    private int authorityB;     // 备机授权状态
    private int recvStatusB;    // 备机接收端状态
    // getters and setters
}
```

## 5. 实现细节

### 5.1 配置文件解析
- 使用`dom4j`解析`platforms.xml`文件
- 正确处理GBK编码
- 实现对格式错误XML的错误处理

### 5.2 HTTP通信
- 使用标准JDK `HttpURLConnection`进行状态查询
- 实现适当的连接超时和错误处理
- 使用字符串操作解析响应体的键值对

### 5.3 线程处理
- 将`ServiceScheduler`实现为`Runnable`
- 使用`Thread.sleep()`设置调度间隔
- 实现适当的线程中断处理

### 5.4 日志记录
- 使用`commons-logging`配合`log4j`后端
- 为不同组件配置适当的日志级别
- 在日志消息中包含上下文信息

## 6. 错误处理

### 6.1 配置错误
- 优雅地处理缺少配置文件的情况
- 验证XML结构和必需字段
- 提供有意义的错误消息

### 6.2 网络错误
- 为HTTP请求实现重试逻辑
- 处理连接超时和网络故障
- 在可能的情况下回退到缓存的状态信息

### 6.3 服务操作错误
- 在操作前验证节点可用性
- 处理服务启动/停止失败
- 为部分失败实现回滚机制

## 7. 配置

### 7.1 系统属性
- `scheduler.interval`：调度循环间隔（默认：30000ms）
- `scheduler.config.path`：platforms.xml路径（默认：/etc/unimas/tomcat/conf/platforms.xml）
- `scheduler.status.url`：状态服务URL（默认：http://127.0.0.1:9001）

### 7.2 日志配置
```properties
log4j.logger.com.scheduler=INFO, stdout
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1} - %m%n
```

## 8. 测试策略

### 8.1 单元测试
- 使用各种平台配置测试XML解析
- 使用不同响应格式测试HTTP状态解析
- 测试节点健康评估逻辑

### 8.2 集成测试
- 测试端到端调度工作流
- 在各种故障场景下测试服务切换
- 测试网络连接问题时的系统行为

### 8.3 性能测试
- 测量调度循环性能
- 测试高负载下的系统行为
- 验证内存使用模式

## 9. 部署

### 9.1 前提条件
- JDK 8运行环境
- 访问配置文件位置的权限
- 到状态服务的网络连接
- 适当的文件系统权限

### 9.2 安装
1. 部署包含所有依赖项的JAR文件
2. 配置系统属性
3. 设置日志配置
4. 启动调度服务

## 10. 未来增强

### 10.1 服务管理
- 实现实际的服务启动/停止操作
- 添加服务健康监控
- 实现服务依赖管理

### 10.2 高可用性
- 添加多个状态服务端点
- 为调度器实现领导者选举
- 添加持久状态管理

### 10.3 监控
- 添加JMX监控端点
- 实现指标收集
- 添加告警功能

---

*本设计文档作为基于s.txt中指定要求实现调度模块的基础。所有实现细节遵循JDK 8标准，并仅使用项目lib目录中可用的库。项目结构不用maven管理，用最原始的结构*