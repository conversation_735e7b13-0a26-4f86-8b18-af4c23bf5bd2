# ¸ùÈÕÖ¾ÅäÖÃ
log4j.rootLogger=INFO, stdout, file

# ¿ØÖÆÌ¨Êä³öÅäÖÃ
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [%t] %c{1} - %m%n

# ÎÄ¼þÊä³öÅäÖÃ
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=logs/scheduler.log
log4j.appender.file.MaxFileSize=10MB
log4j.appender.file.MaxBackupIndex=5
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p [%t] %c{1} - %m%n

# µ÷¶ÈÆ÷°üÈÕÖ¾¼¶±ð
log4j.logger.com.scheduler=INFO, stdout, file
log4j.additivity.com.scheduler=false

# ÅäÖÃ¹ÜÀíÆ÷ÏêÏ¸ÈÕÖ¾
log4j.logger.com.scheduler.config=DEBUG

# ·þÎñ²Ù×÷¹ÜÀíÆ÷ÈÕÖ¾
log4j.logger.com.scheduler.service=INFO

# ºËÐÄµ÷¶ÈÆ÷ÈÕÖ¾
log4j.logger.com.scheduler.core=INFO

# µÚÈý·½¿âÈÕÖ¾¼¶±ð
log4j.logger.org.dom4j=WARN
log4j.logger.org.apache.commons=WARN