package com.scheduler.model;

/**
 * 配置服务信息数据模型
 * 包含servicetype、scheduleid、platform三个字段
 */
public class ConfigServiceInfo {
    private String servicetype;    // 服务类型
    private String scheduleid;     // 调度ID
    private String platform;       // 平台信息

    public ConfigServiceInfo() {
    }

    public ConfigServiceInfo(String servicetype, String scheduleid, String platform) {
        this.servicetype = servicetype;
        this.scheduleid = scheduleid;
        this.platform = platform;
    }

    public String getServicetype() {
        return servicetype;
    }

    public void setServicetype(String servicetype) {
        this.servicetype = servicetype;
    }

    public String getScheduleid() {
        return scheduleid;
    }

    public void setScheduleid(String scheduleid) {
        this.scheduleid = scheduleid;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    @Override
    public String toString() {
        return "ConfigServiceInfo{" +
                "servicetype='" + servicetype + '\'' +
                ", scheduleid='" + scheduleid + '\'' +
                ", platform='" + platform + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ConfigServiceInfo that = (ConfigServiceInfo) o;

        if (servicetype != null ? !servicetype.equals(that.servicetype) : that.servicetype != null) return false;
        if (scheduleid != null ? !scheduleid.equals(that.scheduleid) : that.scheduleid != null) return false;
        return platform != null ? platform.equals(that.platform) : that.platform == null;
    }

    @Override
    public int hashCode() {
        int result = servicetype != null ? servicetype.hashCode() : 0;
        result = 31 * result + (scheduleid != null ? scheduleid.hashCode() : 0);
        result = 31 * result + (platform != null ? platform.hashCode() : 0);
        return result;
    }
}