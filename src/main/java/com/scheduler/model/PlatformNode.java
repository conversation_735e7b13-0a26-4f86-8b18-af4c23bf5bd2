package com.scheduler.model;

/**
 * 平台节点数据模型
 * 表示从platforms.xml配置文件中读取的平台节点信息
 */
public class PlatformNode {
    private String name;        // 平台名称
    private String deviceId;    // 设备号
    private String address;     // 地址
    private int port;           // 端口
    private String mode;        // 模式
    private int id;             // ID
    private String nicId;       // 网卡ID
    private String virtualIp;   // 虚拟IP
    private String netmask;     // 网络掩码

    public PlatformNode() {
    }

    public PlatformNode(String name, String deviceId, String address, int port, 
                       String mode, int id, String nicId, String virtualIp, String netmask) {
        this.name = name;
        this.deviceId = deviceId;
        this.address = address;
        this.port = port;
        this.mode = mode;
        this.id = id;
        this.nicId = nicId;
        this.virtualIp = virtualIp;
        this.netmask = netmask;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNicId() {
        return nicId;
    }

    public void setNicId(String nicId) {
        this.nicId = nicId;
    }

    public String getVirtualIp() {
        return virtualIp;
    }

    public void setVirtualIp(String virtualIp) {
        this.virtualIp = virtualIp;
    }

    public String getNetmask() {
        return netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    @Override
    public String toString() {
        return "PlatformNode{" +
                "name='" + name + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", address='" + address + '\'' +
                ", port=" + port +
                ", mode='" + mode + '\'' +
                ", id=" + id +
                ", nicId='" + nicId + '\'' +
                ", virtualIp='" + virtualIp + '\'' +
                ", netmask='" + netmask + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PlatformNode that = (PlatformNode) o;

        if (port != that.port) return false;
        if (id != that.id) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (deviceId != null ? !deviceId.equals(that.deviceId) : that.deviceId != null) return false;
        if (address != null ? !address.equals(that.address) : that.address != null) return false;
        if (mode != null ? !mode.equals(that.mode) : that.mode != null) return false;
        if (nicId != null ? !nicId.equals(that.nicId) : that.nicId != null) return false;
        if (virtualIp != null ? !virtualIp.equals(that.virtualIp) : that.virtualIp != null) return false;
        return netmask != null ? netmask.equals(that.netmask) : that.netmask == null;
    }

    @Override
    public int hashCode() {
        int result = name != null ? name.hashCode() : 0;
        result = 31 * result + (deviceId != null ? deviceId.hashCode() : 0);
        result = 31 * result + (address != null ? address.hashCode() : 0);
        result = 31 * result + port;
        result = 31 * result + (mode != null ? mode.hashCode() : 0);
        result = 31 * result + id;
        result = 31 * result + (nicId != null ? nicId.hashCode() : 0);
        result = 31 * result + (virtualIp != null ? virtualIp.hashCode() : 0);
        result = 31 * result + (netmask != null ? netmask.hashCode() : 0);
        return result;
    }
}