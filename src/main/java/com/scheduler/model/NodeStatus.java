package com.scheduler.model;

/**
 * 节点状态数据模型
 * 表示从状态机获取的节点状态信息
 */
public class NodeStatus {
    private int businessM;      // 主机业务口状态（1=正常，0=异常）
    private int isolateM;       // 主机隔离口状态（1=正常，0=异常）
    private int authorityM;     // 主机授权状态（1=正常，0=异常）
    private int recvStatusM;    // 主机接收端状态（1=正常，0=异常）
    private int businessB;      // 备机业务口状态（1=正常，0=异常）
    private int isolateB;       // 备机隔离口状态（1=正常，0=异常）
    private int authorityB;     // 备机授权状态（1=正常，0=异常）
    private int recvStatusB;    // 备机接收端状态（1=正常，0=异常）

    public NodeStatus() {
    }

    public NodeStatus(int businessM, int isolateM, int authorityM, int recvStatusM,
                     int businessB, int isolateB, int authorityB, int recvStatusB) {
        this.businessM = businessM;
        this.isolateM = isolateM;
        this.authorityM = authorityM;
        this.recvStatusM = recvStatusM;
        this.businessB = businessB;
        this.isolateB = isolateB;
        this.authorityB = authorityB;
        this.recvStatusB = recvStatusB;
    }

    public int getBusinessM() {
        return businessM;
    }

    public void setBusinessM(int businessM) {
        this.businessM = businessM;
    }

    public int getIsolateM() {
        return isolateM;
    }

    public void setIsolateM(int isolateM) {
        this.isolateM = isolateM;
    }

    public int getAuthorityM() {
        return authorityM;
    }

    public void setAuthorityM(int authorityM) {
        this.authorityM = authorityM;
    }

    public int getRecvStatusM() {
        return recvStatusM;
    }

    public void setRecvStatusM(int recvStatusM) {
        this.recvStatusM = recvStatusM;
    }

    public int getBusinessB() {
        return businessB;
    }

    public void setBusinessB(int businessB) {
        this.businessB = businessB;
    }

    public int getIsolateB() {
        return isolateB;
    }

    public void setIsolateB(int isolateB) {
        this.isolateB = isolateB;
    }

    public int getAuthorityB() {
        return authorityB;
    }

    public void setAuthorityB(int authorityB) {
        this.authorityB = authorityB;
    }

    public int getRecvStatusB() {
        return recvStatusB;
    }

    public void setRecvStatusB(int recvStatusB) {
        this.recvStatusB = recvStatusB;
    }

    /**
     * 检查主机是否健康
     * @return 如果所有主机状态都正常返回true
     */
    public boolean isMasterHealthy() {
        return businessM == 1 && isolateM == 1 && authorityM == 1;
    }

    /**
     * 检查备机是否健康
     * @return 如果所有备机状态都正常返回true
     */
    public boolean isBackupHealthy() {
        return businessB == 1 && isolateB == 1 && authorityB == 1 ;
    }

    /**
     * 检查是否有可用节点
     * @return 如果主机或备机任一健康返回true
     */
    public boolean hasHealthyNode() {
        return isMasterHealthy() || isBackupHealthy();
    }

    @Override
    public String toString() {
        return "NodeStatus{" +
                "businessM=" + businessM +
                ", isolateM=" + isolateM +
                ", authorityM=" + authorityM +
                ", recvStatusM=" + recvStatusM +
                ", businessB=" + businessB +
                ", isolateB=" + isolateB +
                ", authorityB=" + authorityB +
                ", recvStatusB=" + recvStatusB +
                ", masterHealthy=" + isMasterHealthy() +
                ", backupHealthy=" + isBackupHealthy() +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        NodeStatus that = (NodeStatus) o;

        if (businessM != that.businessM) return false;
        if (isolateM != that.isolateM) return false;
        if (authorityM != that.authorityM) return false;
        if (recvStatusM != that.recvStatusM) return false;
        if (businessB != that.businessB) return false;
        if (isolateB != that.isolateB) return false;
        if (authorityB != that.authorityB) return false;
        return recvStatusB == that.recvStatusB;
    }

    @Override
    public int hashCode() {
        int result = businessM;
        result = 31 * result + isolateM;
        result = 31 * result + authorityM;
        result = 31 * result + recvStatusM;
        result = 31 * result + businessB;
        result = 31 * result + isolateB;
        result = 31 * result + authorityB;
        result = 31 * result + recvStatusB;
        return result;
    }
}