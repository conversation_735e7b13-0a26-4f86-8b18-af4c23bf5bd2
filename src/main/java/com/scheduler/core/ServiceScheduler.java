package com.scheduler.core;

import com.scheduler.config.ConfigurationManager;
import com.scheduler.model.NodeStatus;
import com.scheduler.model.PlatformNode;
import com.scheduler.service.ServiceOperationManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 服务调度类
 * 主要负责服务的调度，在专用线程中运行
 */
public class ServiceScheduler implements Runnable {
    private static final Log logger = LogFactory.getLog(ServiceScheduler.class);
    
    // 默认调度间隔（毫秒）
    private static final long DEFAULT_SCHEDULING_INTERVAL = 30000L; // 30秒
    
    private final ConfigurationManager configManager;
    private final ServiceOperationManager serviceManager;
    private final long schedulingInterval;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicBoolean stopRequested = new AtomicBoolean(false);
    
    private Thread schedulerThread;
    private NodeStatus lastKnownStatus;
    private List<PlatformNode> availableNodes;

    /**
     * 默认构造函数
     */
    public ServiceScheduler() {
        this(new ConfigurationManager(), new ServiceOperationManager());
    }

    /**
     * 自定义构造函数
     */
    public ServiceScheduler(ConfigurationManager configManager, ServiceOperationManager serviceManager) {
        this.configManager = configManager;
        this.serviceManager = serviceManager;
        this.schedulingInterval = Long.parseLong(System.getProperty("scheduler.interval", 
                String.valueOf(DEFAULT_SCHEDULING_INTERVAL)));
        
        logger.info("ServiceScheduler初始化完成，调度间隔: " + schedulingInterval + "ms");
    }

    /**
     * 启动调度器
     */
    public synchronized void start() {
        if (running.get()) {
            logger.warn("调度器已经在运行中");
            return;
        }
        
        logger.info("启动服务调度器...");
        
        // 初始化可用节点列表
        try {
            availableNodes = configManager.getNodes();
            logger.info("加载了 " + availableNodes.size() + " 个节点配置");
        } catch (ConfigurationManager.ConfigurationException e) {
            logger.error("加载节点配置失败", e);
            throw new RuntimeException("无法启动调度器：节点配置加载失败", e);
        }
        
        stopRequested.set(false);
        running.set(true);
        
        schedulerThread = new Thread(this, "ServiceScheduler-Thread");
        schedulerThread.setDaemon(false);
        schedulerThread.start();
        
        logger.info("服务调度器启动成功");
    }

    /**
     * 停止调度器
     */
    public synchronized void stop() {
        if (!running.get()) {
            logger.warn("调度器未在运行");
            return;
        }
        
        logger.info("停止服务调度器...");
        
        stopRequested.set(true);
        
        if (schedulerThread != null) {
            schedulerThread.interrupt();
            try {
                schedulerThread.join(5000); // 等待5秒
                if (schedulerThread.isAlive()) {
                    logger.warn("调度器线程未能在5秒内停止");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("等待调度器停止时被中断");
            }
        }
        
        running.set(false);
        logger.info("服务调度器已停止");
    }

    /**
     * 检查调度器是否正在运行
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 主要的调度线程逻辑
     */
    @Override
    public void run() {
        logger.info("调度线程开始运行");
        
        while (!Thread.currentThread().isInterrupted() && !stopRequested.get()) {
            try {
                // 执行一次调度循环
                performSchedulingCycle();
                
                // 等待下一个调度周期
                Thread.sleep(schedulingInterval);
                
            } catch (InterruptedException e) {
                logger.info("调度线程被中断，准备退出");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("调度循环失败", e);
                
                // 发生异常时短暂等待再继续
                try {
                    Thread.sleep(Math.min(schedulingInterval / 2, 10000)); // 最多等待10秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        running.set(false);
        logger.info("调度线程已退出");
    }

    /**
     * 执行一次完整的调度循环
     */
    private void performSchedulingCycle() {
        logger.debug("开始执行调度循环");
        
        try {
            // 步骤1：获取当前节点状态
            NodeStatus currentStatus = configManager.getNodeStatus();
            logger.debug("当前节点状态: " + currentStatus);
            
            // 步骤2：评估当前节点并查找备选方案
            boolean currentNodeHealthy = evaluateNodeHealth(currentStatus);
            List<PlatformNode> healthyNodes = findHealthyNodes();
            
            logger.info("当前节点健康状态: " + currentNodeHealthy + 
                       ", 可用健康节点数量: " + healthyNodes.size());
            
            // 步骤3：如需要则执行服务切换
            if (!currentNodeHealthy && !healthyNodes.isEmpty()) {
                logger.warn("检测到当前节点不健康，开始执行服务切换");
                PlatformNode targetNode = selectBestNode(healthyNodes);
                performServiceSwitching(targetNode);
            } else if (currentNodeHealthy) {
                logger.debug("当前节点健康，无需切换服务");
            } else {
                logger.error("当前节点不健康但没有可用的备选节点！");
            }
            
            // 更新最后已知状态
            lastKnownStatus = currentStatus;
            
        } catch (ConfigurationManager.ConfigurationException e) {
            logger.error("获取节点状态失败", e);
            
            // 如果有缓存的状态，可以考虑使用
            if (lastKnownStatus != null) {
                logger.warn("使用最后已知的节点状态进行评估");
                // 可以基于缓存状态做一些基本判断
            }
        }
        
        logger.debug("调度循环完成");
    }

    /**
     * 评估节点健康状态
     */
    private boolean evaluateNodeHealth(NodeStatus status) {
        if (status == null) {
            logger.warn("节点状态为空，视为不健康");
            return false;
        }
        
        // 检查是否有任何健康的节点（主机或备机）
        boolean healthy = status.hasHealthyNode();
        
        if (!healthy) {
            logger.warn("节点健康检查失败 - 主机健康: " + status.isMasterHealthy() + 
                       ", 备机健康: " + status.isBackupHealthy());
        }
        
        return healthy;
    }

    /**
     * 查找健康的节点
     */
    private List<PlatformNode> findHealthyNodes() {
        List<PlatformNode> healthyNodes = new java.util.ArrayList<>();
        
        if (availableNodes == null || availableNodes.isEmpty()) {
            logger.warn("没有可用的节点配置");
            return healthyNodes;
        }
        
        for (PlatformNode node : availableNodes) {
            if (isNodeHealthy(node)) {
                healthyNodes.add(node);
                logger.debug("发现健康节点: " + node.getName());
            }
        }
        
        return healthyNodes;
    }

    /**
     * 检查单个节点是否健康
     */
    private boolean isNodeHealthy(PlatformNode node) {
        try {
            // 检查节点连接性
            if (!serviceManager.isNodeReachable(node)) {
                logger.debug("节点不可达: " + node.getName());
                return false;
            }
            
            // 检查节点是否可以承载更多服务
            if (!serviceManager.canStartMoreServices(node)) {
                logger.debug("节点无法承载更多服务: " + node.getName());
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            logger.warn("检查节点健康状态时发生异常: " + node.getName(), e);
            return false;
        }
    }

    /**
     * 选择最佳节点
     */
    private PlatformNode selectBestNode(List<PlatformNode> healthyNodes) {
        if (healthyNodes.isEmpty()) {
            throw new IllegalArgumentException("健康节点列表为空");
        }
        
        // 简单策略：选择第一个健康节点
        // TODO: 可以实现更复杂的选择策略，考虑负载、优先级等因素
        PlatformNode selectedNode = healthyNodes.get(0);
        logger.info("选择目标节点: " + selectedNode.getName() + 
                   " (" + selectedNode.getAddress() + ":" + selectedNode.getPort() + ")");
        
        return selectedNode;
    }

    /**
     * 执行服务切换
     */
    private void performServiceSwitching(PlatformNode targetNode) {
        logger.info("开始执行服务切换到节点: " + targetNode.getName());
        
        try {
            // 步骤1：停止当前节点上所有先前启动的服务
            logger.info("步骤1: 停止当前节点的所有服务");
            boolean stopResult = stopCurrentNodeServices();
            if (!stopResult) {
                logger.error("停止当前节点服务失败，但继续执行切换");
            }
            
            // 步骤2：在目标节点上启动所有服务
            logger.info("步骤2: 在目标节点启动所有服务");
            boolean startResult = serviceManager.startAllServices(targetNode);
            if (startResult) {
                logger.info("服务切换成功完成");
            } else {
                logger.error("在目标节点启动服务失败");
                // TODO: 可以考虑回滚操作
            }
            
        } catch (Exception e) {
            logger.error("执行服务切换时发生异常", e);
            // TODO: 实现异常恢复逻辑
        }
    }

    /**
     * 停止当前节点的服务
     */
    private boolean stopCurrentNodeServices() {
        // 获取当前运行的本地节点
        PlatformNode currentNode = getCurrentNode();
        if (currentNode == null) {
            logger.warn("无法确定当前节点，跳过服务停止");
            return false;
        }
        
        return serviceManager.stopAllServices(currentNode);
    }

    /**
     * 获取当前节点信息
     */
    private PlatformNode getCurrentNode() {
        // TODO: 实现获取当前节点的逻辑
        // 可以通过本地IP地址或配置信息来确定当前节点
        
        if (availableNodes != null && !availableNodes.isEmpty()) {
            // 暂时返回第一个节点作为当前节点
            logger.debug("使用第一个配置节点作为当前节点");
            return availableNodes.get(0);
        }
        
        return null;
    }

    /**
     * 获取最后已知的节点状态
     */
    public NodeStatus getLastKnownStatus() {
        return lastKnownStatus;
    }

    /**
     * 获取可用节点列表
     */
    public List<PlatformNode> getAvailableNodes() {
        return availableNodes != null ? new java.util.ArrayList<>(availableNodes) : 
               new java.util.ArrayList<>();
    }
}