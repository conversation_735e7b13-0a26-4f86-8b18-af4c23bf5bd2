package com.scheduler;

import com.scheduler.config.ConfigurationManager;
import com.scheduler.core.ServiceScheduler;
import com.scheduler.model.NodeStatus;
import com.scheduler.model.PlatformNode;
import com.scheduler.service.ServiceOperationManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;

/**
 * 调度模块主应用程序类
 */
public class SchedulerApplication {
    private static final Log logger = LogFactory.getLog(SchedulerApplication.class);
    
    private ServiceScheduler scheduler;
    private volatile boolean shutdown = false;

    /**
     * 应用程序入口点
     */
    public static void main(String[] args) {
        logger.info("=== 调度模块启动 ===");
        
        SchedulerApplication app = new SchedulerApplication();
        
        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                logger.info("收到关闭信号，正在停止调度模块...");
                app.shutdown();
            }
        }));
        
        try {
            app.run(args);
        } catch (Exception e) {
            logger.error("应用程序运行失败", e);
            System.exit(1);
        }
        
        logger.info("=== 调度模块已退出 ===");
    }

    /**
     * 运行应用程序
     */
    public void run(String[] args) throws Exception {
        // 加载配置
        loadConfiguration();
        
        // 打印系统信息
        printSystemInfo();
        
        // 测试组件
        if (args.length > 0 && "test".equals(args[0])) {
            runTests();
            return;
        }
        
        // 启动调度器
        startScheduler();
        
        // 等待关闭信号
        waitForShutdown();
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        logger.info("加载应用程序配置...");
        
        InputStream is = null;
        try {
            is = getClass().getClassLoader().getResourceAsStream("scheduler.properties");
            if (is != null) {
                Properties props = new Properties();
                props.load(is);
                
                // 将配置设置为系统属性
                for (String key : props.stringPropertyNames()) {
                    if (System.getProperty(key) == null) {
                        System.setProperty(key, props.getProperty(key));
                    }
                }
                
                logger.info("成功加载配置文件");
            } else {
                logger.warn("未找到scheduler.properties配置文件，使用默认配置");
            }
        } catch (IOException e) {
            logger.warn("加载配置文件失败，使用默认配置", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.warn("关闭配置文件流失败", e);
                }
            }
        }
    }

    /**
     * 打印系统信息
     */
    private void printSystemInfo() {
        logger.info("=== 系统信息 ===");
        logger.info("应用名称: " + System.getProperty("app.name", "SchedulerModule"));
        logger.info("应用版本: " + System.getProperty("app.version", "1.0.0"));
        logger.info("Java版本: " + System.getProperty("java.version"));
        logger.info("操作系统: " + System.getProperty("os.name") + " " + System.getProperty("os.version"));
        logger.info("工作目录: " + System.getProperty("user.dir"));
        logger.info("调度间隔: " + System.getProperty("scheduler.interval", "30000") + "ms");
        logger.info("配置文件路径: " + System.getProperty("scheduler.config.path", "默认路径"));
        logger.info("状态服务URL: " + System.getProperty("scheduler.status.url", "默认URL"));
        logger.info("=== 系统信息结束 ===");
    }

    /**
     * 运行测试
     */
    private void runTests() {
        logger.info("=== 开始运行组件测试 ===");
        
        try {
            // 测试配置管理器
            testConfigurationManager();
            
            // 测试服务操作管理器
            testServiceOperationManager();
            
            // 测试数据模型
            testDataModels();
            
            logger.info("=== 所有测试完成 ===");
            
        } catch (Exception e) {
            logger.error("测试执行失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试配置管理器
     */
    private void testConfigurationManager() {
        logger.info("--- 测试配置管理器 ---");
        
        try {
            ConfigurationManager configManager = new ConfigurationManager();
            
            // 测试节点配置读取
            List<PlatformNode> nodes = configManager.getNodes();
            logger.info("读取到 " + nodes.size() + " 个节点:");
            for (PlatformNode node : nodes) {
                logger.info("  - " + node.getName() + " (" + node.getAddress() + ":" + node.getPort() + ")");
            }
            
            // 测试状态获取（这可能会失败，因为状态服务可能不可用）
            try {
                NodeStatus status = configManager.getNodeStatus();
                logger.info("节点状态获取成功: " + status);
            } catch (ConfigurationManager.ConfigurationException e) {
                logger.warn("节点状态获取失败（这是正常的，如果状态服务未运行）: " + e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("配置管理器测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试服务操作管理器
     */
    private void testServiceOperationManager() {
        logger.info("--- 测试服务操作管理器 ---");
        
        try {
            ServiceOperationManager serviceManager = new ServiceOperationManager();
            
            // 创建测试节点
            PlatformNode testNode = new PlatformNode("测试节点", "test001", "127.0.0.1", 8080, 
                                                    "test", 1, "eth0", "192.168.1.100", "255.255.255.0");
            
            // 测试各种操作
            logger.info("测试节点连接性: " + serviceManager.isNodeReachable(testNode));
            logger.info("测试服务容量检查: " + serviceManager.canStartMoreServices(testNode));
            logger.info("测试服务运行状态: " + serviceManager.isServiceRunning("TestService", testNode));
            
            // 测试服务操作
            serviceManager.startService("TestService", testNode);
            serviceManager.stopService("TestService", testNode);
            serviceManager.startAllServices(testNode);
            serviceManager.stopAllServices(testNode);
            
            List<String> runningServices = serviceManager.getRunningServices(testNode);
            logger.info("运行中的服务数量: " + runningServices.size());
            
        } catch (Exception e) {
            logger.error("服务操作管理器测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试数据模型
     */
    private void testDataModels() {
        logger.info("--- 测试数据模型 ---");
        
        try {
            // 测试PlatformNode
            PlatformNode node = new PlatformNode("测试平台", "dev001", "192.168.1.10", 8080, 
                                                "normal", 100, "eth0", "192.168.1.200", "255.255.255.0");
            logger.info("平台节点创建成功: " + node);
            
            // 测试NodeStatus
            NodeStatus status = new NodeStatus(1, 1, 1, 1, 0, 1, 1, 1);
            logger.info("节点状态创建成功: " + status);
            logger.info("主机健康状态: " + status.isMasterHealthy());
            logger.info("备机健康状态: " + status.isBackupHealthy());
            logger.info("整体健康状态: " + status.hasHealthyNode());
            
        } catch (Exception e) {
            logger.error("数据模型测试失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 启动调度器
     */
    private void startScheduler() {
        logger.info("初始化并启动调度器...");
        
        try {
            scheduler = new ServiceScheduler();
            scheduler.start();
            
            logger.info("调度器启动成功，开始监控节点状态");
            
        } catch (Exception e) {
            logger.error("调度器启动失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 等待关闭信号
     */
    private void waitForShutdown() {
        logger.info("应用程序运行中，按 Ctrl+C 退出...");
        
        while (!shutdown) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 关闭应用程序
     */
    public void shutdown() {
        shutdown = true;
        
        if (scheduler != null && scheduler.isRunning()) {
            logger.info("正在停止调度器...");
            scheduler.stop();
        }
        
        logger.info("应用程序关闭完成");
    }
}