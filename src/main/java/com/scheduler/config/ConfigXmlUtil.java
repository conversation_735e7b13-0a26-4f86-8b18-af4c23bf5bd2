package com.scheduler.config;

import com.scheduler.model.ConfigServiceInfo;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Config.xml工具类
 * 负责解析config.xml中的服务配置信息
 */
public class ConfigXmlUtil {
    private static final Logger logger = Logger.getLogger(ConfigXmlUtil.class);
    
    private static final String DEFAULT_CONFIG_XML_PATH = "config.xml";
    
    private final String configXmlPath;

    /**
     * 默认构造函数，使用默认配置文件路径
     */
    public ConfigXmlUtil() {
        this(DEFAULT_CONFIG_XML_PATH);
    }

    /**
     * 自定义构造函数
     * @param configXmlPath config.xml文件路径
     */
    public ConfigXmlUtil(String configXmlPath) {
        this.configXmlPath = configXmlPath;
        logger.info("ConfigXmlUtil初始化完成，配置文件路径: " + configXmlPath);
    }

    /**
     * 返回配置中platform为某个值的所有服务（platform没有值的过滤掉）
     * 服务信息只包含servicetype、scheduleid、platform
     * 
     * @param platformValue 平台值
     * @return 符合条件的服务信息列表
     * @throws ConfigXmlException 解析异常
     */
    public List<ConfigServiceInfo> getServicesByPlatform(String platformValue) throws ConfigXmlException {
        logger.info("获取platform为 '" + platformValue + "' 的所有服务");
        
        List<ConfigServiceInfo> services = new ArrayList<>();
        Document document = parseConfigXml();
        
        try {
            // 查找所有的服务节点
            @SuppressWarnings("unchecked")
            List<Element> serviceNodes = document.selectNodes("//node[map/entry[@key='scheduleid']]");
            
            for (Element serviceNode : serviceNodes) {
                ConfigServiceInfo serviceInfo = extractServiceInfo(serviceNode);
                
                // 过滤：platform必须有值且等于指定值
                if (serviceInfo != null && 
                    serviceInfo.getPlatform() != null && 
                    !serviceInfo.getPlatform().trim().isEmpty() &&
                    platformValue.equals(serviceInfo.getPlatform())) {
                    services.add(serviceInfo);
                    logger.debug("找到匹配服务: " + serviceInfo);
                }
            }
            
            logger.info("找到 " + services.size() + " 个platform为 '" + platformValue + "' 的服务");
            
        } catch (Exception e) {
            throw new ConfigXmlException("解析config.xml时发生错误: " + e.getMessage(), e);
        }
        
        return services;
    }

    /**
     * 返回配置中所有服务信息（platform没有值的过滤掉）
     * 服务信息只包含servicetype、scheduleid、platform
     * 
     * @return 所有有效的服务信息列表
     * @throws ConfigXmlException 解析异常
     */
    public List<ConfigServiceInfo> getAllServices() throws ConfigXmlException {
        logger.info("获取所有有效服务信息");
        
        List<ConfigServiceInfo> services = new ArrayList<>();
        Document document = parseConfigXml();
        
        try {
            // 查找所有的服务节点
            @SuppressWarnings("unchecked")
            List<Element> serviceNodes = document.selectNodes("//node[map/entry[@key='scheduleid']]");
            
            for (Element serviceNode : serviceNodes) {
                ConfigServiceInfo serviceInfo = extractServiceInfo(serviceNode);
                
                // 过滤：platform必须有值
                if (serviceInfo != null && 
                    serviceInfo.getPlatform() != null && 
                    !serviceInfo.getPlatform().trim().isEmpty()) {
                    services.add(serviceInfo);
                    logger.debug("找到有效服务: " + serviceInfo);
                }
            }
            
            logger.info("找到 " + services.size() + " 个有效服务");
            
        } catch (Exception e) {
            throw new ConfigXmlException("解析config.xml时发生错误: " + e.getMessage(), e);
        }
        
        return services;
    }

    /**
     * 返回配置中scheduleid为某个值的信息（platform没有值的过滤掉）
     * 服务信息只包含servicetype、scheduleid、platform
     * 
     * @param scheduleidValue 调度ID值
     * @return 符合条件的服务信息，没找到返回null
     * @throws ConfigXmlException 解析异常
     */
    public ConfigServiceInfo getServiceByScheduleid(String scheduleidValue) throws ConfigXmlException {
        logger.info("获取scheduleid为 '" + scheduleidValue + "' 的服务信息");
        
        Document document = parseConfigXml();
        
        try {
            // 查找指定scheduleid的服务节点
            Element serviceNode = (Element) document.selectSingleNode("//node[map/entry[@key='scheduleid' and @value='" + scheduleidValue + "']]");
            
            if (serviceNode != null) {
                ConfigServiceInfo serviceInfo = extractServiceInfo(serviceNode);
                
                // 过滤：platform必须有值
                if (serviceInfo != null && 
                    serviceInfo.getPlatform() != null && 
                    !serviceInfo.getPlatform().trim().isEmpty()) {
                    logger.info("找到匹配服务: " + serviceInfo);
                    return serviceInfo;
                } else {
                    logger.warn("找到scheduleid为 '" + scheduleidValue + "' 的服务，但platform为空，已过滤");
                }
            } else {
                logger.warn("未找到scheduleid为 '" + scheduleidValue + "' 的服务");
            }
            
        } catch (Exception e) {
            throw new ConfigXmlException("解析config.xml时发生错误: " + e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 解析config.xml文件
     * @return Document对象
     * @throws ConfigXmlException 解析异常
     */
    private Document parseConfigXml() throws ConfigXmlException {
        try {
            File configFile = new File(configXmlPath);
            if (!configFile.exists()) {
                throw new ConfigXmlException("配置文件不存在: " + configXmlPath);
            }
            
            SAXReader reader = new SAXReader();
            reader.setEncoding("UTF-8"); // config.xml使用UTF-8编码
            Document document = reader.read(configFile);
            
            logger.debug("成功解析配置文件: " + configXmlPath);
            return document;
            
        } catch (DocumentException e) {
            throw new ConfigXmlException("XML解析失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new ConfigXmlException("读取配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从服务节点中提取服务信息
     * @param serviceNode 服务节点
     * @return 服务信息对象
     */
    private ConfigServiceInfo extractServiceInfo(Element serviceNode) {
        try {
            Element mapElement = serviceNode.element("map");
            if (mapElement == null) {
                return null;
            }
            
            String servicetype = getEntryValue(mapElement, "servicetype");
            String scheduleid = getEntryValue(mapElement, "scheduleid");
            String platform = getEntryValue(mapElement, "platform");
            
            // scheduleid是必需的，servicetype和platform可以为空但会在调用方过滤
            if (scheduleid == null || scheduleid.trim().isEmpty()) {
                return null;
            }
            
            return new ConfigServiceInfo(servicetype, scheduleid, platform);
            
        } catch (Exception e) {
            logger.warn("提取服务信息时发生异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从map元素中获取指定key的entry值
     * @param mapElement map元素
     * @param key 键名
     * @return 值，不存在返回null
     */
    private String getEntryValue(Element mapElement, String key) {
        Element entryElement = (Element) mapElement.selectSingleNode("entry[@key='" + key + "']");
        if (entryElement != null) {
            return entryElement.attributeValue("value");
        }
        return null;
    }

    /**
     * Config.xml解析异常类
     */
    public static class ConfigXmlException extends Exception {
        public ConfigXmlException(String message) {
            super(message);
        }
        
        public ConfigXmlException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}