package com.scheduler.config;

import com.scheduler.model.NodeStatus;
import com.scheduler.model.PlatformNode;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * 配置信息管理类
 * 负责读取配置文件和系统信息获取
 */
public class ConfigurationManager {
    private static final Log logger = LogFactory.getLog(ConfigurationManager.class);
    
    // 默认配置路径
    private static final String DEFAULT_CONFIG_PATH = "/etc/unimas/tomcat/conf/platforms.xml";
    private static final String DEFAULT_STATUS_URL = "http://127.0.0.1:9001";
    private static final int DEFAULT_CONNECTION_TIMEOUT = 5000; // 5秒
    private static final int DEFAULT_READ_TIMEOUT = 10000; // 10秒
    
    private final String configPath;
    private final String statusUrl;
    private final int connectionTimeout;
    private final int readTimeout;

    /**
     * 默认构造函数，使用系统属性或默认值
     */
    public ConfigurationManager() {
        this.configPath = System.getProperty("scheduler.config.path", DEFAULT_CONFIG_PATH);
        this.statusUrl = System.getProperty("scheduler.status.url", DEFAULT_STATUS_URL);
        this.connectionTimeout = Integer.parseInt(System.getProperty("scheduler.connection.timeout", 
                String.valueOf(DEFAULT_CONNECTION_TIMEOUT)));
        this.readTimeout = Integer.parseInt(System.getProperty("scheduler.read.timeout", 
                String.valueOf(DEFAULT_READ_TIMEOUT)));
        
        logger.info("ConfigurationManager初始化完成 - 配置路径: " + configPath + ", 状态URL: " + statusUrl);
    }

    /**
     * 自定义构造函数
     */
    public ConfigurationManager(String configPath, String statusUrl, int connectionTimeout, int readTimeout) {
        this.configPath = configPath;
        this.statusUrl = statusUrl;
        this.connectionTimeout = connectionTimeout;
        this.readTimeout = readTimeout;
        
        logger.info("ConfigurationManager自定义初始化完成 - 配置路径: " + configPath + ", 状态URL: " + statusUrl);
    }

    /**
     * 节点获取接口
     * 读取本地配置文件/etc/unimas/tomcat/conf/platforms.xml获得信息
     * 
     * @return 平台节点列表
     * @throws ConfigurationException 配置读取异常
     */
    public List<PlatformNode> getNodes() throws ConfigurationException {
        logger.info("开始读取节点配置文件: " + configPath);
        
        List<PlatformNode> nodes = new ArrayList<>();
        
        try {
            File configFile = new File(configPath);
            if (!configFile.exists()) {
                throw new ConfigurationException("配置文件不存在: " + configPath);
            }
            
            SAXReader reader = new SAXReader();
            reader.setEncoding("GBK"); // 设置GBK编码
            Document document = reader.read(configFile);
            
            Element root = document.getRootElement();
            if (!"platforms".equals(root.getName())) {
                throw new ConfigurationException("无效的配置文件格式，根元素应为'platforms'");
            }
            
            @SuppressWarnings("unchecked")
            List<Element> platformElements = root.elements("platform");
            
            for (Element platformElement : platformElements) {
                PlatformNode node = parsePlatformNode(platformElement);
                nodes.add(node);
                logger.debug("解析到节点: " + node.getName() + " (" + node.getAddress() + ":" + node.getPort() + ")");
            }
            
            logger.info("成功读取 " + nodes.size() + " 个节点配置");
            
        } catch (DocumentException e) {
            throw new ConfigurationException("XML解析失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new ConfigurationException("读取配置文件失败: " + e.getMessage(), e);
        }
        
        return nodes;
    }

    /**
     * 解析平台节点元素
     */
    private PlatformNode parsePlatformNode(Element platformElement) throws ConfigurationException {
        try {
            String name = getElementText(platformElement, "name", true);
            String deviceId = getElementText(platformElement, "devid", false);
            String address = getElementText(platformElement, "address", true);
            String portStr = getElementText(platformElement, "port", true);
            String mode = getElementText(platformElement, "mode", false);
            String idStr = getElementText(platformElement, "id", false);
            String nicId = getElementText(platformElement, "nicid", false);
            String virtualIp = getElementText(platformElement, "virtualip", false);
            String netmask = getElementText(platformElement, "netmask", false);
            
            int port = Integer.parseInt(portStr);
            int id = idStr != null ? Integer.parseInt(idStr) : 0;
            
            return new PlatformNode(name, deviceId, address, port, mode, id, nicId, virtualIp, netmask);
            
        } catch (NumberFormatException e) {
            throw new ConfigurationException("节点配置中的数字格式错误: " + e.getMessage(), e);
        }
    }

    /**
     * 获取元素文本内容
     */
    private String getElementText(Element parent, String elementName, boolean required) throws ConfigurationException {
        Element element = parent.element(elementName);
        if (element == null) {
            if (required) {
                throw new ConfigurationException("缺少必需的配置元素: " + elementName);
            }
            return null;
        }
        return element.getTextTrim();
    }

    /**
     * 节点状态接口
     * 从状态机获取所有节点信息，发送HTTP GET请求到固定地址127.0.0.1:9001
     * 
     * @return 节点状态信息
     * @throws ConfigurationException 状态获取异常
     */
    public NodeStatus getNodeStatus() throws ConfigurationException {
        logger.debug("开始获取节点状态，URL: " + statusUrl);
        
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        
        try {
            URL url = new URL(statusUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(connectionTimeout);
            connection.setReadTimeout(readTimeout);
            connection.setRequestProperty("User-Agent", "SchedulerModule/1.0");
            
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new ConfigurationException("HTTP请求失败，响应码: " + responseCode);
            }
            
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            
            String responseBody = response.toString();
            logger.debug("状态服务响应: " + responseBody);
            
            NodeStatus status = parseNodeStatus(responseBody);
            logger.info("成功获取节点状态: " + status);
            
            return status;
            
        } catch (IOException e) {
            throw new ConfigurationException("获取节点状态失败: " + e.getMessage(), e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.warn("关闭Reader失败", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 解析节点状态响应
     * 响应格式：字段用&分隔，key和value用=连接
     */
    private NodeStatus parseNodeStatus(String responseBody) throws ConfigurationException {
        NodeStatus status = new NodeStatus();
        
        if (responseBody == null || responseBody.trim().isEmpty()) {
            throw new ConfigurationException("状态响应为空");
        }
        
        try {
            String[] pairs = responseBody.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length != 2) {
                    logger.warn("跳过无效的键值对: " + pair);
                    continue;
                }
                
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();
                int intValue = Integer.parseInt(value);
                
                switch (key) {
                    case "bussinessM":
                        status.setBusinessM(intValue);
                        break;
                    case "isolateM":
                        status.setIsolateM(intValue);
                        break;
                    case "authoriteM":
                        status.setAuthorityM(intValue);
                        break;
                    case "recvstatusM":
                        status.setRecvStatusM(intValue);
                        break;
                    case "bussinessB":
                        status.setBusinessB(intValue);
                        break;
                    case "isolateB":
                        status.setIsolateB(intValue);
                        break;
                    case "authoriteB":
                        status.setAuthorityB(intValue);
                        break;
                    case "recvstatusB":
                        status.setRecvStatusB(intValue);
                        break;
                    default:
                        logger.warn("未知的状态字段: " + key);
                }
            }
            
        } catch (NumberFormatException e) {
            throw new ConfigurationException("状态值格式错误: " + e.getMessage(), e);
        }
        
        return status;
    }

    /**
     * 配置异常类
     */
    public static class ConfigurationException extends Exception {
        public ConfigurationException(String message) {
            super(message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}