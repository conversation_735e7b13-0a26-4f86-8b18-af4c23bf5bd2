package com.scheduler.config;

import com.scheduler.model.ConfigServiceInfo;
import org.apache.log4j.Logger;


import java.util.List;

/**
 * ConfigXmlUtil测试类
 * 演示如何使用config.xml工具类
 */
public class ConfigXmlUtilTest {
    private static final Logger logger = Logger.getLogger(ConfigXmlUtilTest.class);

    /**
     * 测试方法
     */
    public static void testConfigXmlUtil() {
        logger.info("=== 开始测试ConfigXmlUtil ===");
        
        try {
            // 创建工具类实例
            ConfigXmlUtil configUtil = new ConfigXmlUtil("config.xml");
            
            // 测试1：获取platform为"test1"的所有服务
            logger.info("--- 测试1：获取platform为'test1'的所有服务 ---");
            List<ConfigServiceInfo> servicesForTest1 = configUtil.getServicesByPlatform("test1");
            logger.info("找到 " + servicesForTest1.size() + " 个platform为'test1'的服务:");
            for (ConfigServiceInfo service : servicesForTest1) {
                logger.info("  " + service);
            }
            
            // 测试2：获取所有有效服务
            logger.info("--- 测试2：获取所有有效服务 ---");
            List<ConfigServiceInfo> allServices = configUtil.getAllServices();
            logger.info("找到 " + allServices.size() + " 个有效服务:");
            for (ConfigServiceInfo service : allServices) {
                logger.info("  " + service);
            }
            
            // 测试3：根据scheduleid获取服务信息
            logger.info("--- 测试3：根据scheduleid获取服务信息 ---");
            String[] testScheduleIds = {"24", "18", "999"}; // 999不存在，用于测试
            for (String scheduleId : testScheduleIds) {
                ConfigServiceInfo service = configUtil.getServiceByScheduleid(scheduleId);
                if (service != null) {
                    logger.info("scheduleid为'" + scheduleId + "'的服务: " + service);
                } else {
                    logger.info("未找到scheduleid为'" + scheduleId + "'的有效服务");
                }
            }
            
        } catch (ConfigXmlUtil.ConfigXmlException e) {
            logger.error("测试过程中发生异常", e);
        }
        
        logger.info("=== ConfigXmlUtil测试完成 ===");
    }

    /**
     * 主方法，用于独立测试
     */
    public static void main(String[] args) {
        testConfigXmlUtil();
    }
}