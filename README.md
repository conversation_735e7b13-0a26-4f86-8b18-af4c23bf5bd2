# 调度模块 (Scheduler Module)

## 概述

调度模块是一个基于JDK 8的Java应用程序，负责节点状态监控和服务切换。当检测到节点异常时，该模块会自动执行服务切换逻辑，确保系统的高可用性。

## 功能特性

- **节点状态监控**: 通过HTTP接口实时监控节点状态
- **自动服务切换**: 当节点异常时自动切换到健康节点
- **配置文件管理**: 支持XML格式的平台节点配置
- **线程安全**: 使用专用线程进行调度，支持优雅启动和停止
- **详细日志**: 完整的日志记录和错误处理机制

## 系统架构

### 核心组件

1. **配置信息管理类 (ConfigurationManager)**
   - 读取和解析platforms.xml配置文件
   - 获取节点状态信息（通过HTTP接口）
   - 处理GBK编码和错误处理

2. **Config.xml工具类 (ConfigXmlUtil)**
   - 解析config.xml配置文件
   - 按platform筛选服务信息
   - 按scheduleid查找服务信息
   - 获取所有有效服务列表

3. **服务操作管理类 (ServiceOperationManager)**
   - 管理服务启停操作
   - 检查节点连接性和服务容量
   - 预留的服务管理接口

4. **服务调度类 (ServiceScheduler)**
   - 主要调度逻辑协调器
   - 运行在专用线程中
   - 执行节点健康检查和服务切换

5. **数据模型**
   - `PlatformNode`: 平台节点信息
   - `NodeStatus`: 节点状态信息
   - `ConfigServiceInfo`: 配置服务信息

## 技术规范

- **JDK版本**: JDK 8
- **源码编码**: GBK
- **第三方库**: 仅使用lib目录下的JAR文件
- **XML解析**: dom4j-1.4.jar
- **日志框架**: commons-logging + log4j
- **JSON处理**: json-lib-2.2.2-jdk15.jar
- **配置管理**: commons-configuration-1.6.jar

## 项目结构

```
scheduler/
├── src/main/java/com/scheduler/
│   ├── SchedulerApplication.java          # 主应用程序类
│   ├── config/
│   │   ├── ConfigurationManager.java     # 配置管理器
│   │   ├── ConfigXmlUtil.java            # Config.xml工具类
│   │   ├── ConfigServiceInfo.java        # 配置服务信息模型
│   │   └── ConfigXmlUtilTest.java        # Config.xml工具类测试
│   ├── core/
│   │   └── ServiceScheduler.java         # 服务调度器
│   ├── model/
│   │   ├── PlatformNode.java            # 平台节点模型
│   │   └── NodeStatus.java              # 节点状态模型
│   └── service/
│       └── ServiceOperationManager.java  # 服务操作管理器
├── src/main/resources/
│   ├── platforms.xml                     # 平台配置文件
│   ├── log4j.properties                  # 日志配置
│   └── scheduler.properties              # 应用配置
├── lib/                                   # 第三方JAR库
├── logs/                                  # 日志目录
├── build.bat                             # 构建脚本
├── manifest.txt                          # JAR清单文件
└── README.md                             # 本文件
```

## 配置说明

### 系统属性

| 属性名 | 默认值 | 说明 |
|--------|--------|------|
| `scheduler.interval` | 30000 | 调度循环间隔（毫秒） |
| `scheduler.config.path` | /etc/unimas/tomcat/conf/platforms.xml | 平台配置文件路径 |
| `scheduler.status.url` | http://127.0.0.1:9001 | 状态服务URL |
| `scheduler.connection.timeout` | 5000 | HTTP连接超时（毫秒） |
| `scheduler.read.timeout` | 10000 | HTTP读取超时（毫秒） |

### 平台配置文件 (platforms.xml)

```xml
<?xml version="1.0" encoding="GBK"?>
<platforms>
    <platform>
        <name>平台A</name>
        <devid>设备号</devid>
        <address>************</address>
        <port>8080</port>
        <mode>nor</mode>
        <id>201</id>
        <nicid>eth3</nicid>
        <virtualip>*************</virtualip>
        <netmask>*************</netmask>
    </platform>
</platforms>
```

### 节点状态格式

状态服务返回格式（字段用&分隔，键值用=连接）：
```
bussinessM=1&isolateM=1&authoriteM=1&recvstatusM=1&bussinessB=0&isolateB=1&authoriteB=1&recvstatusB=1
```

状态字段说明：
- `bussinessM`: 主机业务口状态 (1=正常, 0=异常)
- `isolateM`: 主机隔离口状态 (1=正常, 0=异常)
- `authoriteM`: 主机授权状态 (1=正常, 0=异常)
- `recvstatusM`: 主机接收端状态 (1=正常, 0=异常)
- `bussinessB`: 备机业务口状态 (1=正常, 0=异常)
- `isolateB`: 备机隔离口状态 (1=正常, 0=异常)
- `authoriteB`: 备机授权状态 (1=正常, 0=异常)
- `recvstatusB`: 备机接收端状态 (1=正常, 0=异常)

### Config.xml配置文件使用

Config.xml工具类提供了三个主要接口：

```java
// 1. 获取指定platform的所有服务
ConfigXmlUtil configUtil = new ConfigXmlUtil("config.xml");
List<ConfigServiceInfo> services = configUtil.getServicesByPlatform("test1");

// 2. 获取所有有效服务（platform不为空）
List<ConfigServiceInfo> allServices = configUtil.getAllServices();

// 3. 根据scheduleid获取服务信息
ConfigServiceInfo service = configUtil.getServiceByScheduleid("24");
```

## 构建和运行

### 前提条件

- JDK 8或更高版本
- 所有必需的JAR文件位于lib目录

### 编译

1. 使用提供的构建脚本（自动处理GBK编码）：
```cmd
build.bat
```

2. 或手动编译（注意指定GBK编码）：
```cmd
javac -encoding GBK -cp "lib/*" -d build/classes -sourcepath src/main/java src/main/java/com/scheduler/*.java src/main/java/com/scheduler/*/*.java
```

### 运行

1. 正常运行调度器：
```cmd
java -cp "build/dist/*" com.scheduler.SchedulerApplication
```

2. 运行测试模式：
```cmd
java -cp "build/dist/*" com.scheduler.SchedulerApplication test
```

### 自定义配置运行

```cmd
java -Dscheduler.interval=60000 -Dscheduler.config.path=custom_platforms.xml -cp "build/dist/*" com.scheduler.SchedulerApplication
```

## 调度逻辑

调度器按以下步骤执行：

1. **状态获取**: 调用节点状态接口获取当前状态
2. **健康评估**: 
   - 检查本机状态可用性
   - 识别可用的备选节点
   - 确定是否需要服务切换
3. **服务迁移**:
   - 停止当前节点上所有先前启动的服务
   - 在可用目标节点上启动所有服务
4. **循环完成**: 等待下一个调度周期

## 日志配置

日志文件位置：`logs/scheduler.log`

日志级别配置：
- 调度器包：INFO级别
- 配置管理器：DEBUG级别
- 第三方库：WARN级别

## 错误处理

### 配置错误
- 优雅处理缺少配置文件
- 验证XML结构和必需字段
- 提供有意义的错误消息

### 网络错误
- HTTP请求重试逻辑
- 连接超时处理
- 回退到缓存状态信息

### 服务操作错误
- 操作前验证节点可用性
- 处理服务启停失败
- 部分失败回滚机制

## 扩展性

### 当前实现状态

- ✅ 配置文件读取和解析
- ✅ 节点状态监控
- ✅ 调度逻辑框架
- ✅ Config.xml工具类
- ⏳ 服务启停操作（接口已预留）
- ⏳ 节点连接性检查（接口已预留）
- ⏳ 服务健康监控（接口已预留）

### 未来增强

1. **服务管理**
   - 实现实际的服务启停操作
   - 添加服务健康监控
   - 实现服务依赖管理

2. **高可用性**
   - 添加多个状态服务端点
   - 实现调度器领导者选举
   - 添加持久状态管理

3. **监控**
   - 添加JMX监控端点
   - 实现指标收集
   - 添加告警功能

## 故障排除

### 常见问题

1. **配置文件未找到**
   - 检查路径配置
   - 确认文件存在且可读

2. **状态服务连接失败**
   - 检查网络连接
   - 确认状态服务运行状态
   - 检查防火墙设置

3. **调度器无响应**
   - 检查日志文件
   - 确认JVM内存设置
   - 检查线程状态

### 调试模式

启用详细日志：
```cmd
java -Dlog4j.logger.com.scheduler=DEBUG -cp "build/dist/*" com.scheduler.SchedulerApplication
```

## 许可证

本项目基于原始设计文档实现，严格遵循JDK 8标准和项目要求。

## 版本历史

- **v1.0.0** - 初始实现
  - 基础调度框架
  - 配置管理和状态监控
  - 预留服务操作接口

---

*本实现基于设计文档严格开发，提供了完整的调度模块框架和扩展接口。*